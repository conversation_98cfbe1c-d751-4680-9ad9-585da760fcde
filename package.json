{"name": "ecommerce-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@clerk/clerk-react": "^5.32.0", "@headlessui/react": "^1.7.17", "@hookform/resolvers": "^5.1.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.80.10", "axios": "^1.10.0", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "mongodb": "^6.17.0", "pg": "^8.16.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.21.0", "recharts": "^2.15.3", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5", "vitest": "^3.2.4"}}