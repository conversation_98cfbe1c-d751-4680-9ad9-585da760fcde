# Database Configuration
DATABASE_URL=*********************************************
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=9934

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/ecommerce_products

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=myVerySecretKeyThatIsAtLeast256BitsLongForHS512Algorithm

# Email Configuration (Optional - for testing)
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=password

# Frontend URL
FRONTEND_URL=http://localhost:3000

# File Upload
FILE_UPLOAD_DIR=./uploads

# Payment Gateways (Optional - use test keys)
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret

# Server Configuration
PORT=8080
SPRING_PROFILES_ACTIVE=dev
